import { ethers } from 'ethers';
import { getExchangeRateCorrection } from '../lib/portfolioCorrection';

// Helper functions for testing (copied from dexUtils.ts)
function calculateTokenPrice(
  numeratorAmount: bigint,
  numeratorDecimals: number,
  denominatorAmount: bigint,
  denominatorDecimals: number
): number {
  if (denominatorAmount === 0n) {
    return 0;
  }

  const numeratorDecimal = parseFloat(ethers.formatUnits(numeratorAmount, numeratorDecimals));
  const denominatorDecimal = parseFloat(ethers.formatUnits(denominatorAmount, denominatorDecimals));

  if (denominatorDecimal === 0 || !isFinite(numeratorDecimal) || !isFinite(denominatorDecimal)) {
    return 0;
  }

  const price = numeratorDecimal / denominatorDecimal;
  return isFinite(price) && price >= 0 ? price : 0;
}

function applyCorrectionToBigInt(value: bigint, correctionFactor: number): bigint {
  if (correctionFactor >= 1) {
    return value;
  }

  const precision = 1000000000000; // 12 decimal places of precision
  const correctionNumerator = BigInt(Math.floor(correctionFactor * precision));
  
  return (value * correctionNumerator) / BigInt(precision);
}

describe('DEX Calculations', () => {
  describe('calculateTokenPrice', () => {
    it('should calculate correct price for normal values', () => {
      // 12.02 USDT (6 decimals) / 1000 BLOCKS (18 decimals) = 0.01202 USDT per BLOCKS
      const usdtAmount = ethers.parseUnits('12.02', 6); // 12,020,000
      const blocksAmount = ethers.parseUnits('1000', 18); // 1000 * 10^18
      
      const price = calculateTokenPrice(usdtAmount, 6, blocksAmount, 18);
      
      expect(price).toBeCloseTo(0.01202, 5);
    });

    it('should handle zero denominator', () => {
      const usdtAmount = ethers.parseUnits('12.02', 6);
      const blocksAmount = 0n;
      
      const price = calculateTokenPrice(usdtAmount, 6, blocksAmount, 18);
      
      expect(price).toBe(0);
    });

    it('should handle very large values safely', () => {
      // Test with inflated BLOCKS amount (37+ trillion)
      const usdtAmount = ethers.parseUnits('12.02', 6);
      const inflatedBlocksAmount = ethers.parseUnits('37491228646506.9430', 18);
      
      const price = calculateTokenPrice(usdtAmount, 6, inflatedBlocksAmount, 18);
      
      // Should be a very small but finite number
      expect(price).toBeGreaterThan(0);
      expect(price).toBeLessThan(0.000001);
      expect(isFinite(price)).toBe(true);
    });
  });

  describe('getExchangeRateCorrection', () => {
    it('should return 1.0 for reasonable values', () => {
      const reasonableAmount = ethers.parseUnits('100', 18); // 100 BLOCKS
      const correction = getExchangeRateCorrection(reasonableAmount);
      
      expect(correction).toBe(1.0);
    });

    it('should apply correction for inflated values', () => {
      const inflatedAmount = ethers.parseUnits('37491228646506.9430', 18);
      const correction = getExchangeRateCorrection(inflatedAmount);
      
      // Should apply extreme correction for trillion-scale values
      expect(correction).toBeLessThan(1);
      expect(correction).toBe(0.000000000001); // 1 trillion divisor
    });

    it('should handle edge cases safely', () => {
      const maxBigInt = BigInt('0xffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff');
      const correction = getExchangeRateCorrection(maxBigInt);
      
      // Should not crash and return a valid correction
      expect(typeof correction).toBe('number');
      expect(isFinite(correction)).toBe(true);
      expect(correction).toBeGreaterThan(0);
    });
  });

  describe('applyCorrectionToBigInt', () => {
    it('should return original value when no correction needed', () => {
      const originalValue = ethers.parseUnits('1000', 18);
      const corrected = applyCorrectionToBigInt(originalValue, 1.0);
      
      expect(corrected).toBe(originalValue);
    });

    it('should apply correction accurately', () => {
      const originalValue = ethers.parseUnits('1000000', 18); // 1 million BLOCKS
      const correctionFactor = 0.00001; // Divide by 100,000
      const corrected = applyCorrectionToBigInt(originalValue, correctionFactor);
      
      // Should result in approximately 10 BLOCKS
      const correctedDecimal = parseFloat(ethers.formatUnits(corrected, 18));
      expect(correctedDecimal).toBeCloseTo(10, 1);
    });

    it('should handle extreme corrections', () => {
      const inflatedValue = ethers.parseUnits('37491228646506.9430', 18);
      const extremeCorrection = 0.000000000001; // 1 trillion divisor
      const corrected = applyCorrectionToBigInt(inflatedValue, extremeCorrection);
      
      // Should result in a much smaller, reasonable value
      const correctedDecimal = parseFloat(ethers.formatUnits(corrected, 18));
      expect(correctedDecimal).toBeLessThan(100000); // Less than 100k BLOCKS
      expect(correctedDecimal).toBeGreaterThan(0);
    });
  });

  describe('Realistic Pool Scenarios', () => {
    it('should produce realistic prices for corrected pool', () => {
      // Simulate the problematic pool state
      const inflatedBlocksReserve = ethers.parseUnits('37491228646506.9430', 18);
      const usdtReserve = ethers.parseUnits('12.02', 6);
      
      // Apply correction
      const correction = getExchangeRateCorrection(inflatedBlocksReserve);
      const correctedBlocksReserve = applyCorrectionToBigInt(inflatedBlocksReserve, correction);
      
      // Calculate corrected price
      const correctedPrice = calculateTokenPrice(usdtReserve, 6, correctedBlocksReserve, 18);
      
      // Should result in a reasonable price (not zero, not extremely high)
      expect(correctedPrice).toBeGreaterThan(0.0001); // At least 0.01 cents
      expect(correctedPrice).toBeLessThan(100); // Less than $100 per BLOCKS
      expect(isFinite(correctedPrice)).toBe(true);
      
      console.log('Corrected BLOCKS price:', correctedPrice.toFixed(6), 'USDT');
      console.log('Corrected BLOCKS reserve:', ethers.formatUnits(correctedBlocksReserve, 18));
    });

    it('should calculate reasonable total liquidity', () => {
      const usdtReserve = ethers.parseUnits('12.02', 6);
      const totalLiquidity = usdtReserve * 2n; // Standard calculation
      
      const liquidityDecimal = parseFloat(ethers.formatUnits(totalLiquidity, 6));
      
      expect(liquidityDecimal).toBeCloseTo(24.04, 2);
    });
  });
});
