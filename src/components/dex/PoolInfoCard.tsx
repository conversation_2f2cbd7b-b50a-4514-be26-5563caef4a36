import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { Badge } from '../ui/Badge';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Info, 
  TrendingUp, 
  TrendingDown,
  Droplets,
  Users,
  DollarSign,
  Percent,
  RefreshCw,
  ExternalLink,
  AlertTriangle,
  Activity
} from 'lucide-react';
import { usePoolInfo, useDEXFormatting } from '../../hooks/useDEX';
import { useWeb3 } from '../../providers/Web3Provider';
import { useEnhancedBalances } from '../../hooks/useContracts';
import { getExchangeRateCorrection } from '../../lib/portfolioCorrection';
import { getPancakeSwapUrl, appKitConfig } from '../../lib/appkit';
import { ethers } from 'ethers';

// Helper functions for safe decimal arithmetic
function calculateTokenPrice(
  numeratorAmount: bigint,
  numeratorDecimals: number,
  denominatorAmount: bigint,
  denominatorDecimals: number
): number {
  if (denominatorAmount === 0n) {
    return 0;
  }

  const numeratorDecimal = parseFloat(ethers.formatUnits(numeratorAmount, numeratorDecimals));
  const denominatorDecimal = parseFloat(ethers.formatUnits(denominatorAmount, denominatorDecimals));

  if (denominatorDecimal === 0 || !isFinite(numeratorDecimal) || !isFinite(denominatorDecimal)) {
    return 0;
  }

  const price = numeratorDecimal / denominatorDecimal;
  return isFinite(price) && price >= 0 ? price : 0;
}

function applyCorrectionToBigInt(value: bigint, correctionFactor: number): bigint {
  if (correctionFactor >= 1) {
    return value;
  }

  const precision = *************; // 12 decimal places of precision
  const correctionNumerator = BigInt(Math.floor(correctionFactor * precision));

  return (value * correctionNumerator) / BigInt(precision);
}

interface PoolInfoCardProps {
  className?: string;
  showUserPosition?: boolean;
}

export function PoolInfoCard({ className = '', showUserPosition = true }: PoolInfoCardProps) {
  const { account, isConnected } = useWeb3();
  const { poolInfo, loading, error, refetch } = usePoolInfo();
  const { formatTokenAmount, formatPercentage } = useDEXFormatting();
  const { correctionApplied } = useEnhancedBalances();
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Apply corrections to pool data if needed - always check for inflated values
  const getCorrectedPoolInfo = (poolInfo: any) => {
    if (!poolInfo) return poolInfo;

    // Always check if BLOCKS reserves appear inflated, regardless of correctionApplied flag
    const shareReserveCorrection = getExchangeRateCorrection(poolInfo.reserves.reserveShare);

    if (shareReserveCorrection >= 1) {
      // No correction needed - values are reasonable
      return {
        ...poolInfo,
        correctionApplied: false,
      };
    }

    // Apply correction using safe BigInt arithmetic
    const correctedShareReserve = applyCorrectionToBigInt(poolInfo.reserves.reserveShare, shareReserveCorrection);

    // Recalculate prices using safe decimal arithmetic
    const correctedSharePrice = calculateTokenPrice(
      poolInfo.reserves.reserveUSDT,
      6,
      correctedShareReserve,
      18
    );

    const correctedUsdtPrice = correctedSharePrice > 0 ? 1 / correctedSharePrice : 0;

    console.log('🔧 Pool Info Correction Applied:', {
      originalReserve: poolInfo.reserves.reserveShare.toString(),
      correctedReserve: correctedShareReserve.toString(),
      originalPrice: poolInfo.shareTokenPrice,
      correctedPrice: correctedSharePrice,
      correctionFactor: shareReserveCorrection
    });

    return {
      ...poolInfo,
      reserves: {
        ...poolInfo.reserves,
        reserveShare: correctedShareReserve,
      },
      shareTokenPrice: correctedSharePrice,
      usdtPrice: correctedUsdtPrice,
      correctionApplied: true,
    };
  };

  const correctedPoolInfo = poolInfo ? getCorrectedPoolInfo(poolInfo) : null;

  // Auto-refresh every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
      setLastRefresh(new Date());
    }, 30000);

    return () => clearInterval(interval);
  }, [refetch]);

  const handleRefresh = () => {
    refetch();
    setLastRefresh(new Date());
  };

  const openPancakeSwapPool = () => {
    if (poolInfo) {
      const url = getPancakeSwapUrl('info/pools', undefined, undefined, poolInfo.pairAddress);
      window.open(url, '_blank');
    }
  };

  if (loading && !poolInfo) {
    return (
      <Card className={`${className}`}>
        <CardContent className="flex items-center justify-center py-8">
          <div className="flex items-center gap-2 text-gray-500">
            <RefreshCw className="h-4 w-4 animate-spin" />
            <span>Loading pool information...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`${className}`}>
        <CardContent className="py-6">
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Failed to load pool information: {error?.userMessage || error?.message || 'Unknown error'}
              <Button variant="ghost" size="sm" onClick={handleRefresh} className="ml-2">
                <RefreshCw className="h-4 w-4" />
                Retry
              </Button>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!poolInfo) {
    return (
      <Card className={`${className}`}>
        <CardContent className="py-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              No pool information available. The BLOCKS/USDT pair may not exist yet.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Handle case where no liquidity pair exists
  if (poolInfo.noPairExists) {
    return (
      <Card className={`${className}`}>
        <CardContent className="py-6">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertDescription>
              <div className="space-y-2">
                <p>No liquidity pair exists for BLOCKS/USDT yet.</p>
                <p className="text-sm text-gray-600">
                  To create a liquidity pool, you'll need to be the first to add liquidity.
                  This will establish the initial price ratio between BLOCKS and USDT.
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => window.open('https://pancakeswap.finance/add', '_blank')}
                  className="mt-2"
                >
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Create Pool on PancakeSwap
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  const hasUserPosition = showUserPosition && isConnected && poolInfo.userLPBalance > 0n;

  return (
    <Card className={`${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Droplets className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">BLOCKS/USDT Pool</h3>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={openPancakeSwapPool}
            >
              <ExternalLink className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <p className="text-sm text-gray-600">
          Real-time pool statistics and liquidity information
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Pool Overview */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium">Total Liquidity</span>
            </div>
            <p className="text-lg font-semibold">
              ${formatTokenAmount(poolInfo.totalLiquidity, 6, 2)}
            </p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-1">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium">BLOCKS-LP Token Supply</span>
            </div>
            <p className="text-lg font-semibold">
              {formatTokenAmount(poolInfo.reserves.totalSupply, 18, 4)}
            </p>
          </div>
        </div>

        <Separator />

        {/* Token Reserves */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <Users className="h-4 w-4" />
            Pool Reserves
          </h4>
          
          <div className="space-y-2">
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">BLOCKS</Badge>
                <span className="text-sm">BLOCKS</span>
                {correctedPoolInfo?.correctionApplied && (
                  <Badge variant="secondary" className="text-xs">
                    Corrected
                  </Badge>
                )}
              </div>
              <span className="font-medium">
                {formatTokenAmount(correctedPoolInfo?.reserves.reserveShare || poolInfo.reserves.reserveShare, 18, 4)}
              </span>
            </div>
            
            <div className="flex justify-between items-center p-2 bg-gray-50 rounded">
              <div className="flex items-center gap-2">
                <Badge variant="secondary" className="text-xs">USDT</Badge>
                <span className="text-sm">USDT</span>
              </div>
              <span className="font-medium">
                {formatTokenAmount(poolInfo.reserves.reserveUSDT, 6, 2)}
              </span>
            </div>
          </div>
        </div>

        <Separator />

        {/* Price Information */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Current Prices
          </h4>
          
          <div className="grid grid-cols-2 gap-3">
            <div className="p-2 bg-blue-50 rounded">
              <div className="flex items-center gap-2 text-xs text-blue-600 mb-1">
                <span>1 BLOCKS =</span>
                {correctedPoolInfo?.correctionApplied && (
                  <Badge variant="secondary" className="text-xs">
                    Corrected
                  </Badge>
                )}
              </div>
              <div className="font-semibold text-blue-800">
                ${(correctedPoolInfo?.shareTokenPrice || poolInfo.shareTokenPrice).toFixed(4)} USDT
              </div>
            </div>

            <div className="p-2 bg-green-50 rounded">
              <div className="flex items-center gap-2 text-xs text-green-600 mb-1">
                <span>1 USDT =</span>
                {correctedPoolInfo?.correctionApplied && (
                  <Badge variant="secondary" className="text-xs">
                    Corrected
                  </Badge>
                )}
              </div>
              <div className="font-semibold text-green-800">
                {(correctedPoolInfo?.usdtPrice || poolInfo.usdtPrice).toFixed(4)} BLOCKS
              </div>
            </div>
          </div>
        </div>

        {/* User Position (if connected and has position) */}
        {hasUserPosition && (
          <>
            <Separator />
            <div className="space-y-3">
              <h4 className="text-sm font-medium flex items-center gap-2">
                <Percent className="h-4 w-4" />
                Your Position
              </h4>
              
              <div className="space-y-2">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">BLOCKS-LP Tokens</span>
                  <span className="font-medium">
                    {formatTokenAmount(poolInfo.userLPBalance, 18, 4)}
                  </span>
                </div>
                
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Pool Share</span>
                  <Badge variant="outline">
                    {formatPercentage(poolInfo.userShareOfPool)}
                  </Badge>
                </div>
                
                {/* Estimated position value */}
                <div className="p-2 bg-purple-50 rounded">
                  <div className="text-xs text-purple-600 mb-1">Estimated Position Value</div>
                  <div className="font-semibold text-purple-800">
                    ${((Number(poolInfo.userLPBalance) / Number(poolInfo.reserves.totalSupply)) * 
                       Number(formatTokenAmount(poolInfo.totalLiquidity, 6, 2))).toFixed(2)}
                  </div>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Pool Health Indicators */}
        <Separator />
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Pool Health</h4>
          <div className="flex flex-wrap gap-2">
            <Badge variant="outline" className="text-xs">
              <Activity className="h-3 w-3 mr-1" />
              Active
            </Badge>
            {poolInfo.reserves.totalSupply > 0n && (
              <Badge variant="outline" className="text-xs">
                <Droplets className="h-3 w-3 mr-1" />
                Liquid
              </Badge>
            )}
            {poolInfo.totalLiquidity > 1000n * 10n**6n && (
              <Badge variant="outline" className="text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                Well Funded
              </Badge>
            )}
          </div>
        </div>

        {/* Last Updated */}
        <div className="text-xs text-gray-500 text-center">
          Last updated: {lastRefresh.toLocaleTimeString()}
        </div>
      </CardContent>
    </Card>
  );
}

// Simplified pool stats component for dashboard use
export function PoolStatsWidget({ className = '' }: { className?: string }) {
  const { poolInfo, loading } = usePoolInfo();
  const { formatTokenAmount } = useDEXFormatting();
  const { correctionApplied } = useEnhancedBalances();

  // Apply corrections to pool data if needed - always check for inflated values
  const getCorrectedPoolInfo = (poolInfo: any) => {
    if (!poolInfo) return poolInfo;

    const shareReserveCorrection = getExchangeRateCorrection(poolInfo.reserves.reserveShare);

    if (shareReserveCorrection >= 1) {
      return {
        ...poolInfo,
        correctionApplied: false,
      };
    }

    // Apply correction using safe arithmetic
    const correctedShareReserve = applyCorrectionToBigInt(poolInfo.reserves.reserveShare, shareReserveCorrection);
    const correctedSharePrice = calculateTokenPrice(
      poolInfo.reserves.reserveUSDT,
      6,
      correctedShareReserve,
      18
    );

    return {
      ...poolInfo,
      shareTokenPrice: correctedSharePrice,
      correctionApplied: true,
    };
  };

  const correctedPoolInfo = poolInfo ? getCorrectedPoolInfo(poolInfo) : null;

  if (loading || !poolInfo) {
    return (
      <div className={`p-4 bg-blue-50 rounded-lg ${className}`}>
        <div className="flex items-center gap-2 text-blue-600">
          <Droplets className="h-4 w-4" />
          <span className="text-sm font-medium">Pool Stats</span>
        </div>
        <div className="mt-2 text-xs text-blue-700">
          {loading ? 'Loading...' : 'No pool data'}
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 bg-blue-50 rounded-lg ${className}`}>
      <div className="flex items-center gap-2 text-blue-600 mb-2">
        <Droplets className="h-4 w-4" />
        <span className="text-sm font-medium">BLOCKS/USDT Pool</span>
      </div>
      <div className="space-y-1 text-xs text-blue-700">
        <div className="flex justify-between">
          <span>Total Liquidity:</span>
          <span>${formatTokenAmount(poolInfo.totalLiquidity, 6, 2)}</span>
        </div>
        <div className="flex justify-between items-center">
          <span>BLOCKS Price:</span>
          <div className="flex items-center gap-1">
            <span>${(correctedPoolInfo?.shareTokenPrice || poolInfo.shareTokenPrice).toFixed(4)}</span>
            {correctedPoolInfo?.correctionApplied && (
              <Badge variant="secondary" className="text-xs">
                Corrected
              </Badge>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
