import React, { useState, useEffect, useCallback } from 'react';
import { parseEther, formatUnits } from 'ethers';
import { Card, CardContent, CardHeader } from '../ui/Card';
import { Button } from '../ui/Button';
import { Input } from '../ui/Input';
import { Label } from '../ui/label';
import { Badge } from '../ui/Badge';
import { Separator } from '../ui/separator';
import { Alert, AlertDescription } from '../ui/alert';
import { 
  Plus, 
  Settings, 
  Info,
  Loader2,
  CheckCircle,
  AlertTriangle,
  ArrowDown
} from 'lucide-react';
import { toast } from 'react-hot-toast';
import { useWeb3 } from '../../providers/Web3Provider';
import { useEnhancedBalances, useBalances } from '../../hooks/useContracts';
import { useLiquidity, useDEXFormatting } from '../../hooks/useDEX';
import { AddLiquidityParams, DEFAULT_SLIPPAGE_CONFIG } from '../../lib/dexUtils';

interface AddLiquidityCardProps {
  onLiquidityAdded?: () => void;
}

export function AddLiquidityCard({ onLiquidityAdded }: AddLiquidityCardProps) {
  const { account, isConnected, isCorrectNetwork } = useWeb3();
  const { balances, formattedBalances, loading: balancesLoading, correctionApplied } = useEnhancedBalances();
  const { liquidityQuote, loading, error, getQuote, executeAddLiquidity, clearQuote } = useLiquidity();
  const { formatTokenAmount, parseTokenAmount, formatPercentage } = useDEXFormatting();

  // Form state
  const [shareTokenAmount, setShareTokenAmount] = useState('');
  const [usdtAmount, setUsdtAmount] = useState('');
  const [slippageTolerance, setSlippageTolerance] = useState(0.5);
  const [isAdvancedMode, setIsAdvancedMode] = useState(false);
  const [isExecuting, setIsExecuting] = useState(false);

  // Auto-calculate optimal amounts when one input changes
  const handleShareTokenChange = useCallback(async (value: string) => {
    setShareTokenAmount(value);
    
    if (value && !isNaN(parseFloat(value)) && parseFloat(value) > 0) {
      try {
        const shareAmount = parseTokenAmount(value, 18);
        // For now, let user input both amounts - in a real implementation,
        // you'd calculate the optimal USDT amount based on current pool ratio
        if (usdtAmount) {
          const usdtAmountBigInt = parseTokenAmount(usdtAmount, 6);
          await getQuote(shareAmount, usdtAmountBigInt);
        }
      } catch (error) {
        console.error('Error calculating quote:', error);
      }
    } else {
      clearQuote();
    }
  }, [usdtAmount, getQuote, clearQuote, parseTokenAmount]);

  const handleUsdtChange = useCallback(async (value: string) => {
    setUsdtAmount(value);
    
    if (value && !isNaN(parseFloat(value)) && parseFloat(value) > 0) {
      try {
        const usdtAmountBigInt = parseTokenAmount(value, 6);
        if (shareTokenAmount) {
          const shareAmount = parseTokenAmount(shareTokenAmount, 18);
          await getQuote(shareAmount, usdtAmountBigInt);
        }
      } catch (error) {
        console.error('Error calculating quote:', error);
      }
    } else {
      clearQuote();
    }
  }, [shareTokenAmount, getQuote, clearQuote, parseTokenAmount]);

  // Execute add liquidity transaction
  const handleAddLiquidity = useCallback(async () => {
    if (!account || !liquidityQuote) {
      toast.error('Please connect wallet and get a quote first');
      return;
    }

    setIsExecuting(true);

    try {
      const deadline = Math.floor(Date.now() / 1000) + (5 * 60); // 5 minutes from now
      
      // Calculate minimum amounts with slippage protection
      const slippageBps = BigInt(Math.floor(slippageTolerance * 100));
      const minimumShareToken = liquidityQuote.shareTokenAmount - 
        (liquidityQuote.shareTokenAmount * slippageBps) / 10000n;
      const minimumUSDT = liquidityQuote.usdtAmount - 
        (liquidityQuote.usdtAmount * slippageBps) / 10000n;

      const params: AddLiquidityParams = {
        shareTokenAmount: liquidityQuote.shareTokenAmount,
        usdtAmount: liquidityQuote.usdtAmount,
        minimumShareToken,
        minimumUSDT,
        deadline,
        userAddress: account,
      };

      const tx = await executeAddLiquidity(params);
      
      toast.success('Liquidity added successfully!');
      
      // Clear form
      setShareTokenAmount('');
      setUsdtAmount('');
      clearQuote();
      
      // Notify parent component
      if (onLiquidityAdded) {
        onLiquidityAdded();
      }

      return tx;
    } catch (error: any) {
      console.error('Failed to add liquidity:', error);
      toast.error(error.message || 'Failed to add liquidity');
    } finally {
      setIsExecuting(false);
    }
  }, [account, liquidityQuote, slippageTolerance, executeAddLiquidity, clearQuote, onLiquidityAdded]);

  // Validation
  const isValidInput = shareTokenAmount && usdtAmount &&
    !isNaN(parseFloat(shareTokenAmount)) && !isNaN(parseFloat(usdtAmount)) &&
    parseFloat(shareTokenAmount) > 0 && parseFloat(usdtAmount) > 0;

  const hasInsufficientBalance = isValidInput && (
    parseTokenAmount(shareTokenAmount, 18) > balances.share ||
    parseTokenAmount(usdtAmount, 6) > balances.usdt
  );

  const canExecute = isConnected && isCorrectNetwork && isValidInput && 
    !hasInsufficientBalance && liquidityQuote && !loading && !isExecuting;

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Plus className="h-5 w-5 text-blue-600" />
            <h3 className="text-lg font-semibold">Add Liquidity</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsAdvancedMode(!isAdvancedMode)}
          >
            <Settings className="h-4 w-4" />
          </Button>
        </div>
        <p className="text-sm text-gray-600">
          Add BLOCKS and USDT to the liquidity pool to earn BLOCKS-LP tokens
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* ShareToken Input */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="shareToken">BLOCKS Amount</Label>
            <div className="flex items-center gap-2">
              <span className="text-xs text-gray-500">
                Balance: {formattedBalances.share}
              </span>
              {correctionApplied && (
                <Badge variant="secondary" className="text-xs">
                  Corrected
                </Badge>
              )}
            </div>
          </div>
          <div className="relative">
            <Input
              id="shareToken"
              type="number"
              placeholder="0.0"
              value={shareTokenAmount}
              onChange={(e) => handleShareTokenChange(e.target.value)}
              className="pr-20"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Badge variant="secondary" className="text-xs">BLOCKS</Badge>
            </div>
          </div>
        </div>

        {/* Arrow */}
        <div className="flex justify-center">
          <ArrowDown className="h-4 w-4 text-gray-400" />
        </div>

        {/* USDT Input */}
        <div className="space-y-2">
          <div className="flex justify-between items-center">
            <Label htmlFor="usdt">USDT Amount</Label>
            <span className="text-xs text-gray-500">
              Balance: {formattedBalances.usdt}
            </span>
          </div>
          <div className="relative">
            <Input
              id="usdt"
              type="number"
              placeholder="0.0"
              value={usdtAmount}
              onChange={(e) => handleUsdtChange(e.target.value)}
              className="pr-16"
            />
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
              <Badge variant="secondary" className="text-xs">USDT</Badge>
            </div>
          </div>
        </div>

        {/* Advanced Settings */}
        {isAdvancedMode && (
          <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
            <div className="space-y-2">
              <Label htmlFor="slippage">Slippage Tolerance (%)</Label>
              <Input
                id="slippage"
                type="number"
                step="0.1"
                min="0.1"
                max="50"
                value={slippageTolerance}
                onChange={(e) => setSlippageTolerance(parseFloat(e.target.value) || 0.5)}
              />
            </div>
          </div>
        )}

        {/* Quote Display */}
        {liquidityQuote && (
          <div className="space-y-2 p-3 bg-blue-50 rounded-lg">
            <div className="flex items-center gap-2">
              <Info className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">Liquidity Preview</span>
            </div>
            <div className="space-y-1 text-xs text-blue-700">
              <div className="flex justify-between">
                <span>BLOCKS-LP Tokens Received:</span>
                <span>{formatTokenAmount(liquidityQuote.lpTokensReceived, 18, 4)}</span>
              </div>
              <div className="flex justify-between">
                <span>Share of Pool:</span>
                <span>{formatPercentage(liquidityQuote.shareOfPool)}</span>
              </div>
              <div className="flex justify-between">
                <span>Price Impact:</span>
                <span className={liquidityQuote.priceImpact > 2 ? 'text-red-600' : 'text-green-600'}>
                  {formatPercentage(liquidityQuote.priceImpact)}
                </span>
              </div>
            </div>
          </div>
        )}

        {/* Error Display */}
        {error && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Insufficient Balance Warning */}
        {hasInsufficientBalance && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Insufficient balance. Please reduce the amount or add more tokens to your wallet.
            </AlertDescription>
          </Alert>
        )}

        {/* Add Liquidity Button */}
        <Button
          onClick={handleAddLiquidity}
          disabled={!canExecute}
          className="w-full"
          size="lg"
        >
          {isExecuting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Adding Liquidity...
            </>
          ) : loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Getting Quote...
            </>
          ) : !isConnected ? (
            'Connect Wallet'
          ) : !isCorrectNetwork ? (
            'Switch to BSC Testnet'
          ) : !isValidInput ? (
            'Enter Amounts'
          ) : hasInsufficientBalance ? (
            'Insufficient Balance'
          ) : !liquidityQuote ? (
            'Get Quote'
          ) : (
            'Add Liquidity'
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
