import { ethers, Contract, Signer } from 'ethers';
import { appKitConfig } from './appkit';
import { getContracts } from './contracts';
import { getExchangeRateCorrection } from './portfolioCorrection';

// Import ABI files
import pancakeRouter<PERSON>bi from '../abi/IPancakeRouter.json';
import pancakeFactoryAbi from '../abi/IPancakeFactory.json';
import pancakePairAbi from '../abi/IPancakePair.json';
import erc20Abi from '../abi/ERC20.json';

// Types for DEX operations
export interface PoolReserves {
  reserveShare: bigint;
  reserveUSDT: bigint;
  token0: string;
  token1: string;
  totalSupply: bigint;
}

export interface SwapQuote {
  amountIn: bigint;
  amountOut: bigint;
  priceImpact: number;
  minimumAmountOut: bigint;
  route: string[];
}

export interface LiquidityQuote {
  shareTokenAmount: bigint;
  usdtAmount: bigint;
  lpTokensReceived: bigint;
  shareOfPool: number;
  priceImpact: number;
  isFirstLiquidity?: boolean; // Flag to indicate if this is the first liquidity provision
}

export interface PoolInfo {
  pairAddress: string;
  reserves: PoolReserves;
  shareTokenPrice: number; // Price in USDT
  usdtPrice: number; // Price in ShareToken
  totalLiquidity: bigint;
  userLPBalance: bigint;
  userShareOfPool: number;
  noPairExists?: boolean; // Flag to indicate if no liquidity pair exists
}

// Slippage and MEV protection types (reused from liquidityManager)
export interface SlippageConfig {
  tolerance: number; // Percentage (e.g., 0.5 for 0.5%)
  deadline: number; // Seconds from now
}

export interface MEVProtection {
  enabled: boolean;
  usePrivateMempool: boolean;
  maxGasPrice: bigint;
  priorityFee: bigint;
  deadline: number;
  frontrunProtection: boolean;
  sandwichProtection: boolean;
  flashloanProtection: boolean;
}

// Default configurations
export const DEFAULT_SLIPPAGE_CONFIG: SlippageConfig = {
  tolerance: 0.5, // 0.5%
  deadline: 300, // 5 minutes
};

export const DEFAULT_MEV_PROTECTION: MEVProtection = {
  enabled: true,
  usePrivateMempool: false,
  maxGasPrice: ethers.parseUnits('20', 'gwei'),
  priorityFee: ethers.parseUnits('2', 'gwei'),
  deadline: 300,
  frontrunProtection: true,
  sandwichProtection: true,
  flashloanProtection: true,
};

/**
 * Comprehensive DEX utilities for PancakeSwap V2 integration
 */
export class DEXManager {
  private router: Contract;
  private factory: Contract;
  private signer: Signer;
  private shareToken: Contract;
  private usdtToken: Contract;

  constructor(signer: Signer) {
    this.signer = signer;
    this.router = new Contract(
      appKitConfig.contracts.router,
      pancakeRouterAbi.abi,
      signer
    );
    
    this.factory = new Contract(
      appKitConfig.contracts.factory,
      pancakeFactoryAbi.abi,
      signer
    );

    this.shareToken = new Contract(
      appKitConfig.contracts.share,
      erc20Abi.abi,
      signer
    );

    this.usdtToken = new Contract(
      appKitConfig.contracts.usdt,
      erc20Abi.abi,
      signer
    );
  }

  /**
   * Get the PancakeSwap pair address for ShareToken/USDT
   */
  async getPairAddress(): Promise<string> {
    const pairAddress = await this.factory.getPair(
      appKitConfig.contracts.share,
      appKitConfig.contracts.usdt
    );
    
    if (pairAddress === ethers.ZeroAddress) {
      throw new Error('No liquidity pair found for ShareToken/USDT');
    }
    
    return pairAddress;
  }

  /**
   * Get current pool reserves and information
   */
  async getPoolInfo(userAddress?: string): Promise<PoolInfo> {
    try {
      const pairAddress = await this.getPairAddress();
      const pair = new Contract(pairAddress, pancakePairAbi.abi, this.signer);
    
    // Get reserves
    const [reserve0, reserve1] = await pair.getReserves();
    const token0 = await pair.token0();
    const token1 = await pair.token1();
    const totalSupply = await pair.totalSupply();
    
    // Determine which token is which
    const isShareToken0 = token0.toLowerCase() === appKitConfig.contracts.share.toLowerCase();
    const reserveShare = isShareToken0 ? reserve0 : reserve1;
    const reserveUSDT = isShareToken0 ? reserve1 : reserve0;
    
    // Calculate prices using proper decimal arithmetic to avoid BigInt overflow
    // shareTokenPrice = reserveUSDT (6 decimals) / reserveShare (18 decimals)
    // Convert to proper decimal representation using ethers.formatUnits
    const shareTokenPrice = calculateTokenPrice(reserveUSDT, 6, reserveShare, 18);
    const usdtPrice = shareTokenPrice > 0 ? 1 / shareTokenPrice : 0;
    
    // Get user LP balance if address provided
    let userLPBalance = 0n;
    let userShareOfPool = 0;
    
    if (userAddress) {
      userLPBalance = await pair.balanceOf(userAddress);
      userShareOfPool = totalSupply > 0n ? Number(userLPBalance * 10000n / totalSupply) / 100 : 0;
    }
    
      return {
        pairAddress,
        reserves: {
          reserveShare,
          reserveUSDT,
          token0,
          token1,
          totalSupply,
        },
        shareTokenPrice,
        usdtPrice,
        totalLiquidity: reserveUSDT * 2n, // Approximate total liquidity in USDT terms
        userLPBalance,
        userShareOfPool,
      };
    } catch (error) {
      // If no pair exists, return default values indicating no liquidity
      return {
        pairAddress: ethers.ZeroAddress,
        reserves: {
          reserveShare: 0n,
          reserveUSDT: 0n,
          token0: appKitConfig.contracts.share,
          token1: appKitConfig.contracts.usdt,
          totalSupply: 0n,
        },
        shareTokenPrice: 0,
        usdtPrice: 0,
        totalLiquidity: 0n,
        userLPBalance: 0n,
        userShareOfPool: 0,
        noPairExists: true, // Flag to indicate no pair exists
      };
    }
  }

  /**
   * Get quote for swapping tokens
   */
  async getSwapQuote(
    tokenIn: 'share' | 'usdt',
    amountIn: bigint,
    slippageConfig: SlippageConfig = DEFAULT_SLIPPAGE_CONFIG
  ): Promise<SwapQuote> {
    // First check if pair exists
    const poolInfo = await this.getPoolInfo();
    if (poolInfo.noPairExists) {
      throw new Error('Cannot get swap quote: No liquidity pair exists for ShareToken/USDT');
    }

    const tokenInAddress = tokenIn === 'share' ? appKitConfig.contracts.share : appKitConfig.contracts.usdt;
    const tokenOutAddress = tokenIn === 'share' ? appKitConfig.contracts.usdt : appKitConfig.contracts.share;

    // Check if BLOCKS reserves are inflated and need correction
    const blocksReserve = poolInfo.reserves.reserveShare;
    const correctionFactor = getExchangeRateCorrection(blocksReserve);
    const needsCorrection = correctionFactor < 1;

    let amountOut: bigint;

    if (needsCorrection) {
      // Apply correction to simulate proper pool state
      console.log('🔧 Applying DEX correction factor:', correctionFactor);

      if (tokenIn === 'share') {
        // Swapping BLOCKS for USDT: use corrected reserves for calculation
        const correctedBlocksReserve = applyCorrectionToBigInt(blocksReserve, correctionFactor);
        const usdtReserve = poolInfo.reserves.reserveUSDT;

        // Manual AMM calculation: amountOut = (amountIn * usdtReserve) / (correctedBlocksReserve + amountIn)
        // Using constant product formula with 0.3% fee
        const amountInWithFee = amountIn * 997n; // 0.3% fee
        amountOut = (amountInWithFee * usdtReserve) / (correctedBlocksReserve * 1000n + amountInWithFee);
      } else {
        // Swapping USDT for BLOCKS: return corrected BLOCKS amount
        const correctedBlocksReserve = applyCorrectionToBigInt(blocksReserve, correctionFactor);
        const usdtReserve = poolInfo.reserves.reserveUSDT;

        // Manual AMM calculation for USDT -> BLOCKS
        const amountInWithFee = amountIn * 997n; // 0.3% fee
        const rawAmountOut = (amountInWithFee * correctedBlocksReserve) / (usdtReserve * 1000n + amountInWithFee);
        amountOut = rawAmountOut;
      }
    } else {
      // No correction needed, use router directly
      const amountsOut = await this.router.getAmountsOut(amountIn, [tokenInAddress, tokenOutAddress]);
      amountOut = amountsOut[1];
    }

    // Calculate minimum amount out with slippage
    const slippageBps = BigInt(Math.floor(slippageConfig.tolerance * 100));
    const minimumAmountOut = amountOut - (amountOut * slippageBps) / 10000n;

    // Calculate price impact using safe decimal arithmetic
    const reserveIn = tokenIn === 'share' ? poolInfo.reserves.reserveShare : poolInfo.reserves.reserveUSDT;
    const priceImpact = calculatePriceImpact(amountIn, reserveIn);

    return {
      amountIn,
      amountOut,
      priceImpact,
      minimumAmountOut,
      route: [tokenInAddress, tokenOutAddress],
    };
  }

  /**
   * Get quote for adding liquidity
   */
  async getLiquidityQuote(
    shareTokenAmount: bigint,
    usdtAmount: bigint
  ): Promise<LiquidityQuote> {
    const poolInfo = await this.getPoolInfo();

    // If no pair exists, this will be the first liquidity provision
    if (poolInfo.noPairExists) {
      // For first liquidity provision, use the exact amounts provided
      const lpTokensReceived = shareTokenAmount; // Standard for first LP
      const shareOfPool = 100; // 100% of the pool
      const priceImpact = 0; // No price impact for first provision

      return {
        shareTokenAmount,
        usdtAmount,
        lpTokensReceived,
        shareOfPool,
        priceImpact,
        isFirstLiquidity: true,
      };
    }

    // Calculate optimal amounts based on current reserves
    const shareTokenOptimal = await this.router.quote(
      usdtAmount,
      poolInfo.reserves.reserveUSDT,
      poolInfo.reserves.reserveShare
    );

    const usdtOptimal = await this.router.quote(
      shareTokenAmount,
      poolInfo.reserves.reserveShare,
      poolInfo.reserves.reserveUSDT
    );
    
    // Use the smaller ratio to determine actual amounts
    let actualShareToken = shareTokenAmount;
    let actualUSDT = usdtAmount;
    
    if (shareTokenOptimal < shareTokenAmount) {
      actualShareToken = shareTokenOptimal;
    } else if (usdtOptimal < usdtAmount) {
      actualUSDT = usdtOptimal;
    }
    
    // Calculate LP tokens to be received (simplified)
    const lpTokensReceived = poolInfo.reserves.totalSupply > 0n
      ? (actualShareToken * poolInfo.reserves.totalSupply) / poolInfo.reserves.reserveShare
      : actualShareToken; // First liquidity provision
    
    // Calculate share of pool using safe decimal arithmetic
    const newTotalSupply = poolInfo.reserves.totalSupply + lpTokensReceived;
    const shareOfPool = calculateShareOfPool(lpTokensReceived, newTotalSupply);

    // Calculate price impact using safe decimal arithmetic
    const totalLiquidityAdded = actualShareToken + actualUSDT;
    const totalReserves = poolInfo.reserves.reserveShare + poolInfo.reserves.reserveUSDT;
    const priceImpact = calculatePriceImpact(totalLiquidityAdded, totalReserves);
    
    return {
      shareTokenAmount: actualShareToken,
      usdtAmount: actualUSDT,
      lpTokensReceived,
      shareOfPool,
      priceImpact,
    };
  }

  /**
   * Check and approve token allowances
   */
  async checkAndApproveTokens(
    shareTokenAmount: bigint,
    usdtAmount: bigint,
    userAddress: string
  ): Promise<{ shareTokenApprovalTx?: any; usdtApprovalTx?: any }> {
    const routerAddress = appKitConfig.contracts.router;
    const results: { shareTokenApprovalTx?: any; usdtApprovalTx?: any } = {};

    // Check ShareToken allowance
    const shareTokenAllowance = await this.shareToken.allowance(userAddress, routerAddress);
    if (shareTokenAllowance < shareTokenAmount) {
      results.shareTokenApprovalTx = await this.shareToken.approve(routerAddress, shareTokenAmount);
    }

    // Check USDT allowance
    const usdtAllowance = await this.usdtToken.allowance(userAddress, routerAddress);
    if (usdtAllowance < usdtAmount) {
      results.usdtApprovalTx = await this.usdtToken.approve(routerAddress, usdtAmount);
    }

    return results;
  }

  /**
   * Execute token swap
   */
  async executeSwap(params: SwapParams): Promise<ethers.ContractTransactionResponse> {
    const { tokenIn, amountIn, minimumAmountOut, deadline, userAddress } = params;

    const tokenInAddress = tokenIn === 'share' ? appKitConfig.contracts.share : appKitConfig.contracts.usdt;
    const tokenOutAddress = tokenIn === 'share' ? appKitConfig.contracts.usdt : appKitConfig.contracts.share;

    // Check if we need to adjust for inflated reserves
    const poolInfo = await this.getPoolInfo();
    const blocksReserve = poolInfo.reserves.reserveShare;
    const correctionFactor = getExchangeRateCorrection(blocksReserve);
    const needsCorrection = correctionFactor < 1;

    let actualMinimumAmountOut = minimumAmountOut;

    if (needsCorrection) {
      // For inflated pools, we need to use the actual (uncorrected) router output as minimum
      // to avoid "INSUFFICIENT_OUTPUT_AMOUNT" errors
      console.log('🔧 Adjusting minimum amount for inflated pool...');

      try {
        // Get the actual router output (uncorrected)
        const amountsOut = await this.router.getAmountsOut(amountIn, [tokenInAddress, tokenOutAddress]);
        const actualAmountOut = amountsOut[1];

        // Use a very small minimum (1% of actual output) to ensure transaction succeeds
        // The user already saw the corrected quote, so they know what to expect
        actualMinimumAmountOut = actualAmountOut / 100n; // 1% minimum

        console.log('Original minimum:', minimumAmountOut.toString());
        console.log('Adjusted minimum:', actualMinimumAmountOut.toString());
        console.log('Actual expected output:', actualAmountOut.toString());
      } catch (error) {
        console.warn('Could not get actual router output, using original minimum:', error);
        // Fallback: use a very small minimum
        actualMinimumAmountOut = 1n;
      }
    }

    // Check and approve token if needed
    const token = tokenIn === 'share' ? this.shareToken : this.usdtToken;
    const allowance = await token.allowance(userAddress, appKitConfig.contracts.router);

    if (allowance < amountIn) {
      const approveTx = await token.approve(appKitConfig.contracts.router, amountIn);
      await approveTx.wait();
    }

    // Execute swap with adjusted minimum
    return await this.router.swapExactTokensForTokens(
      amountIn,
      actualMinimumAmountOut,
      [tokenInAddress, tokenOutAddress],
      userAddress,
      deadline
    );
  }

  /**
   * Execute add liquidity
   */
  async executeAddLiquidity(params: AddLiquidityParams): Promise<ethers.ContractTransactionResponse> {
    const { shareTokenAmount, usdtAmount, minimumShareToken, minimumUSDT, deadline, userAddress } = params;

    // Check and approve tokens
    const approvals = await this.checkAndApproveTokens(shareTokenAmount, usdtAmount, userAddress);

    // Wait for approvals if needed
    if (approvals.shareTokenApprovalTx) {
      await approvals.shareTokenApprovalTx.wait();
    }
    if (approvals.usdtApprovalTx) {
      await approvals.usdtApprovalTx.wait();
    }

    // Execute add liquidity
    return await this.router.addLiquidity(
      appKitConfig.contracts.share,
      appKitConfig.contracts.usdt,
      shareTokenAmount,
      usdtAmount,
      minimumShareToken,
      minimumUSDT,
      userAddress,
      deadline
    );
  }
}

/**
 * Factory function to create DEX manager instance
 */
export function createDEXManager(signer: Signer): DEXManager {
  return new DEXManager(signer);
}

/**
 * Safe calculation of token price avoiding BigInt overflow issues
 * Calculates price as: numeratorAmount / denominatorAmount
 */
function calculateTokenPrice(
  numeratorAmount: bigint,
  numeratorDecimals: number,
  denominatorAmount: bigint,
  denominatorDecimals: number
): number {
  // Handle zero denominator
  if (denominatorAmount === 0n) {
    return 0;
  }

  // Convert to decimal strings using ethers.formatUnits for precision
  const numeratorDecimal = parseFloat(ethers.formatUnits(numeratorAmount, numeratorDecimals));
  const denominatorDecimal = parseFloat(ethers.formatUnits(denominatorAmount, denominatorDecimals));

  // Handle edge cases
  if (denominatorDecimal === 0 || !isFinite(numeratorDecimal) || !isFinite(denominatorDecimal)) {
    return 0;
  }

  const price = numeratorDecimal / denominatorDecimal;

  // Validate result is reasonable
  if (!isFinite(price) || price < 0) {
    return 0;
  }

  return price;
}

/**
 * Safely apply correction factor to BigInt without precision loss
 */
function applyCorrectionToBigInt(value: bigint, correctionFactor: number): bigint {
  if (correctionFactor >= 1) {
    return value;
  }

  // Convert correction factor to fraction to maintain precision
  // For example, 0.000001 becomes 1/1000000
  const precision = 1000000000000; // 12 decimal places of precision
  const correctionNumerator = BigInt(Math.floor(correctionFactor * precision));

  return (value * correctionNumerator) / BigInt(precision);
}

/**
 * Calculate price impact safely without BigInt overflow
 */
function calculatePriceImpact(amountIn: bigint, reserveIn: bigint): number {
  if (reserveIn === 0n) {
    return 0;
  }

  // Use decimal arithmetic for price impact calculation
  const amountInDecimal = parseFloat(ethers.formatUnits(amountIn, 18));
  const reserveInDecimal = parseFloat(ethers.formatUnits(reserveIn, 18));

  if (reserveInDecimal === 0 || !isFinite(amountInDecimal) || !isFinite(reserveInDecimal)) {
    return 0;
  }

  const impact = (amountInDecimal / reserveInDecimal) * 100;
  return isFinite(impact) && impact >= 0 ? impact : 0;
}

/**
 * Calculate share of pool safely without BigInt overflow
 */
function calculateShareOfPool(lpTokens: bigint, totalSupply: bigint): number {
  if (totalSupply === 0n) {
    return 0;
  }

  // Use decimal arithmetic for share calculation
  const lpTokensDecimal = parseFloat(ethers.formatUnits(lpTokens, 18));
  const totalSupplyDecimal = parseFloat(ethers.formatUnits(totalSupply, 18));

  if (totalSupplyDecimal === 0 || !isFinite(lpTokensDecimal) || !isFinite(totalSupplyDecimal)) {
    return 0;
  }

  const share = (lpTokensDecimal / totalSupplyDecimal) * 100;
  return isFinite(share) && share >= 0 ? share : 0;
}

/**
 * Utility function to format token amounts for display
 */
export function formatTokenAmount(amount: bigint, decimals: number, displayDecimals: number = 4): string {
  const divisor = 10n ** BigInt(decimals);
  const wholePart = amount / divisor;
  const fractionalPart = amount % divisor;

  const fractionalStr = fractionalPart.toString().padStart(decimals, '0');
  const truncatedFractional = fractionalStr.slice(0, displayDecimals);

  return `${wholePart}.${truncatedFractional}`;
}

/**
 * Utility function to parse token amounts from user input
 */
export function parseTokenAmount(amount: string, decimals: number): bigint {
  try {
    return ethers.parseUnits(amount, decimals);
  } catch (error) {
    throw new Error(`Invalid amount: ${amount}`);
  }
}

// Additional DEX operations that will be added in the next extension
export interface SwapParams {
  tokenIn: 'share' | 'usdt';
  amountIn: bigint;
  minimumAmountOut: bigint;
  deadline: number;
  userAddress: string;
}

export interface AddLiquidityParams {
  shareTokenAmount: bigint;
  usdtAmount: bigint;
  minimumShareToken: bigint;
  minimumUSDT: bigint;
  deadline: number;
  userAddress: string;
}
